package com.taobao.wireless.orange.manager.config;

import com.taobao.wireless.orange.common.constant.enums.ResourceType;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.dal.enhanced.entity.OResourceDO;
import com.taobao.wireless.orange.manager.ResourceManager;
import com.taobao.wireless.orange.manager.config.factory.ConfigGeneratorFactory;
import com.taobao.wireless.orange.manager.config.strategy.ResourceTypeStrategy;
import com.taobao.wireless.orange.manager.model.NamespaceIdNamePairBO;
import com.taobao.wireless.orange.manager.model.ResourceSerializable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 配置管理器
 * 负责协调各种配置生成器和资源管理器，提供统一的配置生成入口
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
@RequiredArgsConstructor
public class ConfigManager {

    private final ConfigGeneratorFactory configGeneratorFactory;
    private final ResourceManager resourceManager;
    private final ResourceTypeStrategy resourceTypeStrategy;

    /**
     * 生成全量发布配置
     *
     * @param namespace 命名空间信息，不能为空
     * @return 生成的资源对象，如果生成失败则抛出异常
     * @throws CommonException 当命名空间无效或生成失败时
     */
    public OResourceDO generateFullReleaseConfig(@Valid @NotNull NamespaceIdNamePairBO namespace) {
        log.info("开始生成全量发布配置，命名空间: {}", namespace.getName());

        try {
            ResourceSerializable config = configGeneratorFactory.getFullReleaseGenerator()
                    .generate(namespace, null);

            return createResourceSafely(config, ResourceType.FULL_RELEASE_CONFIG, "全量发布配置");
        } catch (Exception e) {
            log.error("生成全量发布配置失败，命名空间: {}", namespace.getName(), e);
            throw new CommonException(ExceptionEnum.SYSTEM_ERROR, "生成全量发布配置失败", e);
        }
    }

    /**
     * 生成增量发布配置
     *
     * @param namespace   命名空间信息，不能为空
     * @param baseVersion 基础版本号，不能为空
     * @return 生成的资源对象，如果生成失败则抛出异常
     * @throws CommonException 当参数无效或生成失败时
     */
    public OResourceDO generateIncrementalReleaseConfig(@Valid @NotNull NamespaceIdNamePairBO namespace,
                                                       @NotBlank String baseVersion) {
        log.info("开始生成增量发布配置，命名空间: {}, 基础版本: {}", namespace.getName(), baseVersion);

        try {
            ResourceSerializable config = configGeneratorFactory.getIncrementalReleaseGenerator()
                    .generate(namespace, baseVersion);

            return createResourceSafely(config, ResourceType.INCREMENTAL_RELEASE_CONFIG, "增量发布配置");
        } catch (Exception e) {
            log.error("生成增量发布配置失败，命名空间: {}, 基础版本: {}", namespace.getName(), baseVersion, e);
            throw new CommonException(ExceptionEnum.SYSTEM_ERROR, "生成增量发布配置失败", e);
        }
    }

    /**
     * 生成灰度配置
     *
     * @param namespace 命名空间信息，不能为空
     * @return 生成的资源对象，如果生成失败则抛出异常
     * @throws CommonException 当命名空间无效或生成失败时
     */
    public OResourceDO generateGrayConfig(@Valid @NotNull NamespaceIdNamePairBO namespace) {
        log.info("开始生成灰度配置，命名空间: {}", namespace.getName());

        try {
            ResourceSerializable config = configGeneratorFactory.getGrayConfigGenerator()
                    .generate(namespace, null);

            return createResourceSafely(config, ResourceType.FULL_GRAY_CONFIG, "灰度配置");
        } catch (Exception e) {
            log.error("生成灰度配置失败，命名空间: {}", namespace.getName(), e);
            throw new CommonException(ExceptionEnum.SYSTEM_ERROR, "生成灰度配置失败", e);
        }
    }

    /**
     * 生成实验配置
     *
     * @param appKey 应用标识，不能为空
     * @return 实验配置字符串
     * @throws CommonException 当功能未实现时
     */
    public String generateExperimentConfig(@NotBlank String appKey) {
        log.warn("实验配置生成功能尚未实现，应用标识: {}", appKey);
        throw new CommonException(ExceptionEnum.SYSTEM_ERROR, "实验配置生成功能尚未实现");
    }

    /**
     * 安全地创建资源
     *
     * @param config 配置对象
     * @param resourceType 资源类型
     * @param configTypeName 配置类型名称（用于日志）
     * @return 创建的资源对象
     * @throws CommonException 当配置为空或创建失败时
     */
    private OResourceDO createResourceSafely(ResourceSerializable config, ResourceType resourceType, String configTypeName) {
        if (config == null) {
            throw CommonException.getDynamicException(ExceptionEnum.PARAM_INVALID,
                    "生成的{}为空，可能是因为没有找到相关的配置数据", configTypeName);
        }

        try {
            OResourceDO resource = resourceManager.create(config, resourceType);
            log.info("{}生成成功，资源ID: {}", configTypeName, resource.getResourceId());
            return resource;
        } catch (Exception e) {
            log.error("创建{}资源失败", configTypeName, e);
            throw new CommonException(ExceptionEnum.SYSTEM_ERROR, "创建资源失败", e);
        }
    }
}
