package com.taobao.wireless.orange.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.*;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.manager.config.ConfigManager;
import com.taobao.wireless.orange.manager.config.generator.FullReleaseConfigGenerator;
import com.taobao.wireless.orange.manager.model.*;
import com.taobao.wireless.orange.manager.release.OperationFactory;
import com.taobao.wireless.orange.manager.release.OperationContext;
import com.taobao.wireless.orange.manager.release.AbstractOperationTemplate;
import com.taobao.wireless.orange.manager.util.PageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;
import static com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType.FINISH_RELEASE;

@Service
public class ReleaseOrderManager {
    @Autowired
    private ParameterManager parameterManager;
    @Autowired
    private NamespaceManager namespaceManager;

    @Autowired
    private ConditionManager conditionManager;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private OParameterVersionDAO parameterVersionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private ONamespaceVersionContentDAO namespaceVersionContentDAO;

    @Autowired
    private ConfigManager configManager;

    @Autowired
    private FullReleaseConfigGenerator fullReleaseConfigGenerator;
    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    private OReleaseOrderOperationDAO releaseOrderOperationDAO;
    @Autowired
    private OParameterDAO parameterDAO;
    @Autowired
    private OConditionDAO conditionDAO;
    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;

    // 添加操作策略工厂
    @Autowired
    private OperationFactory operationFactory;

    /**
     * 查询发布单列表
     *
     * @param query
     * @param pagination
     * @return
     */
    public Page<OReleaseOrderDO> query(ReleaseOrderBO query, Pagination pagination) {
        return releaseOrderDAO.lambdaQuery()
                .eq(query.getAppKey() != null, OReleaseOrderDO::getAppKey, query.getAppKey())
                .eq(query.getNamespaceId() != null, OReleaseOrderDO::getNamespaceId, query.getNamespaceId())
                .eq(query.getReleaseVersion() != null, OReleaseOrderDO::getReleaseVersion, query.getReleaseVersion())
                .in(CollectionUtils.isNotEmpty(query.getStatuses()), OReleaseOrderDO::getStatus, query.getStatuses())
                .orderByDesc(OReleaseOrderDO::getId)
                .page(PageUtil.build(pagination));
    }

    /**
     * 查询各个状态的发布单数量
     */
    public Map<ReleaseOrderStatus, Long> countByStatus(String namespaceId) {
        return releaseOrderDAO.lambdaQuery()
                .eq(OReleaseOrderDO::getNamespaceId, namespaceId)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OReleaseOrderDO::getStatus, Collectors.counting()));
    }

    /**
     * 新建发布
     *
     * @param releaseOrderBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String create(ReleaseOrderBO releaseOrderBO) {
        ONamespaceDO namespace = namespaceManager.getByNamespaceId(releaseOrderBO.getNamespaceId());
        releaseOrderBO.setAppKey(namespace.getAppKey());
        // todo: 获取 namespace 锁

        // 检测涉及发布实体是否有在变更中
        checkReleaseObjectIsPublishing(releaseOrderBO);

        // 生成发布单
        String releaseVersion = createReleaseOrder(releaseOrderBO);

        // 新增条件版本记录
        conditionManager.createConditionVersions(namespace, releaseVersion, releaseOrderBO.getConditionVersionBOS());

        // 新增参数版本记录
        parameterManager.createParameterVersions(namespace, releaseVersion, releaseOrderBO.getParameterVersionBOS());

        // 新增参数条件版本记录
        parameterManager.createParameterConditionVersions(namespace, releaseVersion, releaseOrderBO.getParameterVersionBOS(), releaseOrderBO.getConditionVersionBOS());

        // 新增 namespace 版本记录
        namespaceVersionManager.upgradeChangeVersion(releaseOrderBO.getNamespaceId(), releaseVersion, NamespaceVersionChangeType.NEW_RELEASE);

        // todo: 释放 namespace 锁
        return releaseVersion;
    }

    public void publish(String releaseVersion) {
        AbstractOperationTemplate strategy =
            (AbstractOperationTemplate) operationFactory.getStrategy(OperationType.RELEASE);

        OperationContext context = new OperationContext(releaseVersion);
        strategy.execute(context);
    }

    public void cancel(String releaseVersion) {
        AbstractOperationTemplate strategy =
            (AbstractOperationTemplate) operationFactory.getStrategy(OperationType.CANCEL);

        OperationContext context = new OperationContext(releaseVersion);
        strategy.execute(context);
    }

    public void verify(String releaseVersion) {
        AbstractOperationTemplate strategy =
            (AbstractOperationTemplate) operationFactory.getStrategy(OperationType.VERIFY);

        OperationContext context = new OperationContext(releaseVersion);
        strategy.execute(context);
    }

    public void ratioGray(String releaseVersion, int percent) {
        AbstractOperationTemplate strategy =
            (AbstractOperationTemplate) operationFactory.getStrategy(OperationType.RATIO_GRAY);

        OperationContext context = new OperationContext(releaseVersion, percent);
        strategy.execute(context);
    }

    /**
     * 查询发布单变更内容
     *
     * @param releaseVersion
     * @return
     */
    public List<ParameterChangeBO> getChanges(String releaseVersion) {
        // 查询本次发布单涉及的参数
        List<OParameterVersionDO> parameters = parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion).list();
        List<String> parameterIds = parameters.stream()
                .map(OParameterVersionDO::getParameterId)
                .collect(Collectors.toList());
        Map<String, List<OParameterConditionVersionDO>> initParameterConditionVersions = parameterManager.getParameterConditionVersionsByStatus(parameterIds, VersionStatus.INIT, false);

        // 查询修改前参数信息
        var onlineParamId2Version = parameterManager.getOnlineParameterVersions(parameterIds);
        var onlineParamId2ParamCondition = parameterManager.getOnlineParameterConditionVersions(parameterIds);

        return parameters.stream().map(version -> {
            ParameterChangeBO parameterChange = new ParameterChangeBO();

            // 本次修改的内容
            ParameterVersionBO change = BeanUtil.createFromProperties(version, ParameterVersionBO.class);
            Optional.ofNullable(initParameterConditionVersions.get(version.getParameterId()))
                    .ifPresent(i -> {
                        change.setParameterConditionVersionBOS(BeanUtil.createFromProperties(i, ParameterConditionVersionBO.class));
                    });
            parameterChange.setChange(change);

            // 修改前的参数信息（即：线上参数信息
            OParameterVersionDO oldParameterVersion = onlineParamId2Version.get(version.getParameterId());
            if (oldParameterVersion != null) {
                ParameterVersionBO before = BeanUtil.createFromProperties(oldParameterVersion, ParameterVersionBO.class);
                Optional.ofNullable(onlineParamId2ParamCondition.get(version.getParameterId()))
                        .ifPresent(i -> {
                            var conditionId2ParameterCondition = BeanUtil.createFromProperties(i, ParameterConditionVersionBO.class).stream().collect(Collectors.toMap(ParameterConditionVersionBO::getConditionId, Function.identity()));

                            List<ParameterConditionVersionBO> sortedParameterConditions = parameterManager.getConditionsOrder(before).stream().map(conditionId2ParameterCondition::get).collect(Collectors.toList());
                            sortedParameterConditions.add(conditionId2ParameterCondition.get(DEFAULT_CONDITION_ID));

                            before.setParameterConditionVersionBOS(sortedParameterConditions);
                        });
                parameterChange.setBefore(before);
            }

            return parameterChange;
        }).collect(Collectors.toList());

        // todo: 参数变更
//        List<OConditionVersionDO> conditions = conditionVersionDAO.lambdaQuery().eq(OConditionVersionDO::getReleaseVersion, releaseVersion).list();
    }

    /**
     * 获取改动前的参数版本
     *
     * @param releaseVersion
     * @param parameterIds
     * @return
     */
    private Map<String, OParameterVersionDO> getPreviousParameters(String releaseVersion, List<String> parameterIds) {
        ONamespaceVersionDO namespaceVersion = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getReleaseVersion, releaseVersion)
                .eq(ONamespaceVersionDO::getChangeType, FINISH_RELEASE)
                .one();

        // 如果当前还未发布过，则使用线上最新的版本内容
        if (namespaceVersion == null) {
            var onlineParamId2Version = parameterManager.getOnlineParameterVersions(parameterIds);
            var onlineParamId2ParamCondition = parameterManager.getOnlineParameterConditionVersions(parameterIds);
        }

        // 如果已经发布过，则从快照中去取上一个版本内容
        namespaceVersionContentDAO.lambdaQuery()
                .eq(ONamespaceVersionContentDO::getNamespaceId, namespaceVersion.getNamespaceId())
                .le(ONamespaceVersionContentDO::getNamespaceVersion, namespaceVersion.getNamespaceVersion())
                .oneOpt()
                .ifPresent(i -> {
                    try {
                        NamespaceVersionContentBO releaseConfig = JSON.parseObject(i.getContent(), NamespaceVersionContentBO.class);
                    } catch (Exception e) {
                        // todo: fixme
                    }
                });

        return null;
    }

    /**
     * 获取发布单操作记录
     *
     * @param releaseVersion
     * @return
     */
    public List<OReleaseOrderOperationDO> getOperations(String releaseVersion, List<OperationType> operationTypes) {
        return releaseOrderOperationDAO.lambdaQuery()
                .eq(OReleaseOrderOperationDO::getReleaseVersion, releaseVersion)
                .in(CollectionUtils.isNotEmpty(operationTypes), OReleaseOrderOperationDO::getType, operationTypes)
                .orderByDesc(OReleaseOrderOperationDO::getId)
                .list();
    }

    /**
     * 获取发布单详情
     *
     * @param releaseVersion
     * @return
     */
    public ReleaseOrderBO getDetail(String releaseVersion) {
        OReleaseOrderDO releaseOrderDO = releaseOrderDAO.getByReleaseVersion(releaseVersion);
        ReleaseOrderBO releaseOrder = BeanUtil.createFromProperties(releaseOrderDO, ReleaseOrderBO.class);
        List<OParameterVersionDO> parameters = parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .list();
        List<OConditionVersionDO> conditions = conditionVersionDAO.lambdaQuery()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();

        releaseOrder.setParameterVersionBOS(BeanUtil.createFromProperties(parameters, ParameterVersionBO.class));
        releaseOrder.setConditionVersionBOS(BeanUtil.createFromProperties(conditions, ConditionVersionBO.class));
        return releaseOrder;
    }


    private String createReleaseOrder(ReleaseOrderBO releaseOrderBO) {
        String releaseVersion = String.valueOf(SerializeUtil.version());
        releaseOrderBO.setReleaseVersion(releaseVersion);
        releaseOrderBO.setBizType(ReleaseOrderBizType.NAMESPACE);
        releaseOrderBO.setBizId(releaseOrderBO.getNamespaceId());
        releaseOrderBO.setStatus(ReleaseOrderStatus.INIT);
        releaseOrderBO.setReleaseType(ReleaseType.PUBLISH);

        // 新增发布单
        boolean success = releaseOrderDAO.save(releaseOrderBO);
        if (!success) {
            throw new CommonException(ExceptionEnum.CREATE_RELEASE_ORDER_FAIL);
        }
        return releaseVersion;
    }

    /**
     * 检测发布实体是否在发布中
     *
     * @param releaseOrder
     */
    private void checkReleaseObjectIsPublishing(ReleaseOrderBO releaseOrder) {
        if (CollectionUtils.isNotEmpty(releaseOrder.getParameterVersionBOS())) {
            List<String> parameterIds = releaseOrder.getParameterVersionBOS().stream()
                    .map(ParameterVersionBO::getParameterId)
                    .collect(Collectors.toList());

            Long count = parameterVersionDAO.lambdaQuery()
                    .in(OParameterVersionDO::getParameterId, parameterIds)
                    .eq(OParameterVersionDO::getStatus, VersionStatus.INIT)
                    .count();
            if (count > 0) {
                throw new CommonException(ExceptionEnum.PARAMETER_IS_PUBLISHING);
            }
        }

        if (CollectionUtils.isNotEmpty(releaseOrder.getConditionVersionBOS())) {
            List<String> conditionIds = releaseOrder.getConditionVersionBOS().stream()
                    .map(ConditionVersionBO::getConditionId)
                    .collect(Collectors.toList());

            Long count = conditionVersionDAO.lambdaQuery()
                    .in(OConditionVersionDO::getConditionId, conditionIds)
                    .eq(OConditionVersionDO::getStatus, VersionStatus.INIT)
                    .count();
            if (count > 0) {
                throw new CommonException(ExceptionEnum.CONDITION_IS_PUBLISHING);
            }
        }
    }
}