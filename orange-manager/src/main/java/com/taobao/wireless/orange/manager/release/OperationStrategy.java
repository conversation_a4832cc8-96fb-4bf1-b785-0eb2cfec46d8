package com.taobao.wireless.orange.manager.release;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;

/**
 * 发布操作策略接口
 */
public interface OperationStrategy {

    /**
     * 获取操作类型
     */
    OperationType getOperationType();

    /**
     * 状态验证
     * @param context 操作上下文
     */
    void validateStatus(OperationContext context);

    /**
     * 权限校验
     * @param context 操作上下文
     */
    void validatePermission(OperationContext context);

    /**
     * 入参校验
     * @param context 操作上下文
     */
    void validateParameters(OperationContext context);

    /**
     * 执行具体的业务逻辑
     * @param context 操作上下文
     */
    void executeOperation(OperationContext context);

    /**
     * 更新发布单状态
     * @param context 操作上下文
     */
    void updateReleaseOrderStatus(OperationContext context);

    /**
     * 获取目标状态（用于状态更新）
     */
    ReleaseOrderStatus getTargetStatus();
}