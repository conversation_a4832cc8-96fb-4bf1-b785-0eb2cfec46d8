package com.taobao.wireless.orange.manager.release.impl;

import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.*;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.manager.NamespaceVersionManager;
import com.taobao.wireless.orange.manager.release.AbstractOperationTemplate;
import com.taobao.wireless.orange.manager.release.OperationContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 取消操作策略实现
 */
@Component
public class CancelStrategy extends AbstractOperationTemplate {

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Autowired
    private OParameterVersionDAO parameterVersionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private OParameterDAO parameterDAO;

    @Autowired
    private OConditionDAO conditionDAO;

    @Override
    public OperationType getOperationType() {
        return OperationType.CANCEL;
    }

    @Override
    public void validateStatus(OperationContext context) {
        var releaseOrder = context.getReleaseOrder();

        if (releaseOrder.getStatus().isFinished()) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_INVALID);
        }
    }

    @Override
    public void validatePermission(OperationContext context) {
        var namespace = context.getNamespace();
        String workerId = ThreadContextUtil.getWorkerId();

        if (StringUtils.isBlank(workerId) || !namespace.getOwners().contains(workerId)) {
            throw new CommonException(ExceptionEnum.NO_PERMISSION);
        }
    }

    @Override
    public void validateParameters(OperationContext context) {
    }

    @Override
    public ReleaseOrderStatus getTargetStatus() {
        return ReleaseOrderStatus.CANCELED;
    }

    @Override
    public void executeOperation(OperationContext context) {
        String releaseVersion = context.getReleaseVersion();
        var releaseOrder = context.getReleaseOrder();

        // 更新版本对象状态为取消
        updateVersionStatusToCanceled(releaseVersion);

        // 更新namespace版本
        namespaceVersionManager.upgradeChangeVersion(
            releaseOrder.getNamespaceId(),
            releaseVersion,
            NamespaceVersionChangeType.CANCEL_RELEASE
        );

        // 处理新增的参数和条件
        handleNewParametersAndConditions(releaseVersion);
    }

    private void updateVersionStatusToCanceled(String releaseVersion) {
        // 更新参数版本状态
        parameterVersionDAO.lambdaUpdate()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterVersionDO::getStatus, VersionStatus.CANCELED)
                .update();

        // 更新条件版本状态
        conditionVersionDAO.lambdaUpdate()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OConditionVersionDO::getStatus, VersionStatus.CANCELED)
                .update();

        // 更新参数条件版本状态
        parameterConditionVersionDAO.lambdaUpdate()
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterConditionVersionDO::getStatus, VersionStatus.CANCELED)
                .update();
    }

    private void handleNewParametersAndConditions(String releaseVersion) {
        // 处理新增的参数
        List<String> newParameterIds = parameterVersionDAO.lambdaQuery()
                .select(OParameterVersionDO::getParameterId)
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .eq(OParameterVersionDO::getChangeType, ChangeType.CREATE)
                .list()
                .stream()
                .map(OParameterVersionDO::getParameterId)
                .toList();

        if (CollectionUtils.isNotEmpty(newParameterIds)) {
            parameterDAO.lambdaUpdate()
                    .in(OParameterDO::getParameterId, newParameterIds)
                    .set(OParameterDO::getStatus, ParameterStatus.INVALID)
                    .update();
        }

        // 处理新增的条件
        List<String> newConditionIds = conditionVersionDAO.lambdaQuery()
                .select(OConditionVersionDO::getConditionId)
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .eq(OConditionVersionDO::getChangeType, ChangeType.CREATE)
                .list()
                .stream()
                .map(OConditionVersionDO::getConditionId)
                .toList();

        if (CollectionUtils.isNotEmpty(newConditionIds)) {
            conditionDAO.lambdaUpdate()
                    .in(OConditionDO::getConditionId, newConditionIds)
                    .set(OConditionDO::getStatus, ConditionStatus.INVALID)
                    .update();
        }
    }
}