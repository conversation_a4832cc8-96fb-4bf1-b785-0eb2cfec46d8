package com.taobao.wireless.orange.manager.release;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 发布操作策略工厂
 */
@Component
public class OperationFactory {

    private final Map<OperationType, OperationStrategy> strategyMap;

    @Autowired
    public OperationFactory(List<OperationStrategy> strategies) {
        this.strategyMap = strategies.stream()
                .collect(Collectors.toMap(
                    OperationStrategy::getOperationType,
                    Function.identity()
                ));
    }

    /**
     * 根据操作类型获取对应的策略
     */
    public OperationStrategy getStrategy(OperationType operationType) {
        OperationStrategy strategy = strategyMap.get(operationType);
        if (strategy == null) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID,
                "不支持的操作类型: " + operationType);
        }
        return strategy;
    }
}