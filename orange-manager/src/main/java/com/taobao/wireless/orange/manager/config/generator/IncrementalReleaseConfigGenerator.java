package com.taobao.wireless.orange.manager.config.generator;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterVersionDO;
import com.taobao.wireless.orange.manager.model.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class IncrementalReleaseConfigGenerator extends ConfigGenerator<ReleaseConfig> {

    @Override
    protected List<String> getReleaseVersions(String namespaceId, String baseVersion) {
        if (baseVersion == null) {
            return Collections.emptyList();
        }

        return namespaceVersionDAO.lambdaQuery()
                .select(ONamespaceVersionDO::getReleaseVersion)
                .eq(ONamespaceVersionDO::getNamespaceId, namespaceId)
                .gt(ONamespaceVersionDO::getNamespaceChangeVersion, baseVersion)
                .eq(ONamespaceVersionDO::getChangeType, NamespaceVersionChangeType.FINISH_RELEASE)
                .list()
                .stream()
                .map(ONamespaceVersionDO::getReleaseVersion)
                .collect(Collectors.toList());
    }

    @Override
    protected List<OParameterVersionDO> getParameterVersions(String namespaceId, List<String> releaseVersions) {
        if (CollectionUtils.isEmpty(releaseVersions)) {
            return Collections.emptyList();
        }

        return parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getNamespaceId, namespaceId)
                .in(OParameterVersionDO::getReleaseVersion, releaseVersions)
                .eq(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .list();
    }

    @Override
    protected ReleaseConfig buildResult(NamespaceIdNamePairBO namespace, List<Parameter> parameters, List<String> offlineParameters, Map<String, OConditionVersionDO> conditionMap) {
        return ReleaseConfig.builder()
                .schemaVersion("1.0")
                .namespace(namespace.getName())
                .strategy(ConfigStrategy.INCREMENTAL)
                .type(ConfigType.RELEASE)
                .conditions(buildConditions(conditionMap))
                .parameters(parameters != null ? parameters : Collections.emptyList())
                .offlineParameters(offlineParameters != null ? offlineParameters : Collections.emptyList())
                .build();
    }

    /**
     * 构建条件列表
     *
     * @param conditionMap 条件映射表
     * @return 排序后的条件列表
     */
    private List<Condition> buildConditions(Map<String, OConditionVersionDO> conditionMap) {
        if (conditionMap == null || conditionMap.isEmpty()) {
            return Collections.emptyList();
        }

        return conditionMap.values().stream()
                .map(c -> {
                    return Condition.builder()
                            .id(c.getConditionId())
                            .expression(JSON.parse(c.getExpression(), Expression.class))
                            .build();
                })
                // 端上依赖排序，加速检索
                .sorted(Comparator.comparing(Condition::getId))
                .collect(Collectors.toList());
    }

    @Override
    protected List<VersionStatus> getVersionStatuses() {
        return List.of(VersionStatus.RELEASED);
    }
}
