package com.taobao.wireless.orange.manager.config.generator;

import com.taobao.mtop.commons.utils.CollectionUtil;
import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.manager.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 灰度配置生成器
 * 负责生成包含灰度发布单信息的配置文件
 * 支持多个发布单的参数配置和条件管理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GrayConfigGenerator extends ConfigGenerator<GrayConfig> {

    /**
     * 灰度配置支持的发布单状态列表
     */
    private static final List<ReleaseOrderStatus> SUPPORTED_GRAY_STATUSES = List.of(
            ReleaseOrderStatus.IN_RATIO_GRAY,
            ReleaseOrderStatus.VERIFY_PASS,
            ReleaseOrderStatus.WAIT_VERIFY
    );

    @Override
    protected List<String> getReleaseVersions(String namespaceId, String baseVersion) {
        log.debug("获取灰度发布版本列表，命名空间ID: {}", namespaceId);

        List<String> versions = releaseOrderDAO.lambdaQuery()
                .eq(OReleaseOrderDO::getNamespaceId, namespaceId)
                .in(OReleaseOrderDO::getStatus, SUPPORTED_GRAY_STATUSES)
                .orderByDesc(OReleaseOrderDO::getReleaseVersion)
                .list()
                .stream()
                .map(OReleaseOrderDO::getReleaseVersion)
                .collect(Collectors.toList());

        log.debug("找到{}个灰度发布版本", versions.size());
        return versions;
    }

    @Override
    protected List<OParameterVersionDO> getParameterVersions(String namespaceId, List<String> releaseVersions) {
        if (CollectionUtil.isEmpty(releaseVersions)) {
            log.debug("发布版本列表为空，返回空参数版本列表");
            return Collections.emptyList();
        }

        log.debug("获取参数版本列表，命名空间ID: {}, 发布版本数量: {}", namespaceId, releaseVersions.size());

        List<OParameterVersionDO> parameters = parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getNamespaceId, namespaceId)
                .in(OParameterVersionDO::getReleaseVersion, releaseVersions)
                .eq(OParameterVersionDO::getStatus, VersionStatus.INIT)
                .ne(OParameterVersionDO::getChangeType, ChangeType.DELETE)
                .orderByAsc(OParameterVersionDO::getParameterKey)
                .list();

        log.debug("找到{}个参数版本", parameters.size());
        return parameters;
    }

    @Override
    protected GrayConfig buildResult(NamespaceIdNamePairBO namespace, List<Parameter> parameters,
                                   List<String> offlineParameters, Map<String, OConditionVersionDO> conditionMap) {
        log.debug("构建灰度配置结果，命名空间: {}", namespace.getName());

        List<GrayConfig.ReleaseOrder> orders = buildGrayReleaseOrders(namespace.getNamespaceId());
        List<Condition> conditions = buildConditionList(conditionMap);

        GrayConfig config = GrayConfig.builder()
                .schemaVersion("1.0")
                .namespace(namespace.getName())
                .strategy(ConfigStrategy.FULL)
                .type(ConfigType.GRAY)
                .conditions(conditions)
                .orders(orders)
                .build();

        log.debug("灰度配置构建完成，包含{}个发布单，{}个条件", orders.size(), conditions.size());
        return config;
    }

    /**
     * 构建条件列表
     *
     * @param conditionMap 条件映射
     * @return 排序后的条件列表
     */
    private List<Condition> buildConditionList(Map<String, OConditionVersionDO> conditionMap) {
        if (conditionMap == null || conditionMap.isEmpty()) {
            return Collections.emptyList();
        }

        return conditionMap.values().stream()
                .map(this::convertToCondition)
                // 端上依赖排序，加速检索
                .sorted(Comparator.comparing(Condition::getId))
                .collect(Collectors.toList());
    }

    /**
     * 将条件版本DO转换为条件对象
     *
     * @param conditionVersion 条件版本DO
     * @return 条件对象
     */
    private Condition convertToCondition(OConditionVersionDO conditionVersion) {
        return Condition.builder()
                .id(conditionVersion.getConditionId())
                .expression(JSON.parse(conditionVersion.getExpression(), Expression.class))
                .build();
    }

    /**
     * 构建灰度发布单列表
     * 优化数据库查询，减少N+1问题
     *
     * @param namespaceId 命名空间ID
     * @return 灰度发布单列表
     */
    private List<GrayConfig.ReleaseOrder> buildGrayReleaseOrders(String namespaceId) {
        log.debug("开始构建灰度发布单列表，命名空间ID: {}", namespaceId);

        // 获取所有需要的发布版本号
        final List<String> releaseVersions = getReleaseVersions(namespaceId, null);
        if (releaseVersions.isEmpty()) {
            log.debug("没有找到灰度发布版本");
            return Collections.emptyList();
        }

        // 批量获取所有版本的参数，减少数据库查询次数
        final Map<String, List<OParameterVersionDO>> versionToParamsMap = batchLoadParametersByVersions(
                namespaceId, releaseVersions);

        // 构建发布单列表
        List<GrayConfig.ReleaseOrder> orders = releaseVersions.stream()
                .map(version -> buildSingleReleaseOrder(version, versionToParamsMap, namespaceId))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.debug("构建完成{}个灰度发布单", orders.size());
        return orders;
    }

    /**
     * 批量加载指定版本的参数
     *
     * @param namespaceId 命名空间ID
     * @param releaseVersions 发布版本列表
     * @return 版本号到参数列表的映射
     */
    private Map<String, List<OParameterVersionDO>> batchLoadParametersByVersions(String namespaceId,
                                                                                List<String> releaseVersions) {
        List<OParameterVersionDO> allParams = parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getNamespaceId, namespaceId)
                .in(OParameterVersionDO::getReleaseVersion, releaseVersions)
                .eq(OParameterVersionDO::getStatus, VersionStatus.INIT)
                .orderByAsc(OParameterVersionDO::getParameterKey)
                .list();

        // 按版本号分组
        return allParams.stream()
                .collect(Collectors.groupingBy(OParameterVersionDO::getReleaseVersion));
    }

    /**
     * 构建单个发布单
     *
     * @param version 发布版本号
     * @param versionToParamsMap 版本到参数的映射
     * @param namespaceId 命名空间ID
     * @return 发布单对象，如果没有参数则返回null
     */
    private GrayConfig.ReleaseOrder buildSingleReleaseOrder(String version,
                                                           Map<String, List<OParameterVersionDO>> versionToParamsMap,
                                                           String namespaceId) {
        final List<OParameterVersionDO> parameterList = versionToParamsMap.getOrDefault(version, Collections.emptyList());
        if (parameterList.isEmpty()) {
            log.debug("版本{}没有参数，跳过", version);
            return null;
        }

        // 分离在线和离线参数
        ParameterPartition partition = partitionParameters(parameterList);

        // 构建参数列表
        List<Parameter> parameters = buildParametersForReleaseOrder(partition.getOnlineParameters(), namespaceId);

        return GrayConfig.ReleaseOrder.builder()
                .version(Long.parseLong(version))
                .parameters(parameters)
                .offlineParameters(partition.getOfflineParameterKeys())
                .build();
    }

    /**
     * 分离在线和离线参数
     *
     * @param parameterList 参数列表
     * @return 参数分区结果
     */
    private ParameterPartition partitionParameters(List<OParameterVersionDO> parameterList) {
        Map<Boolean, List<OParameterVersionDO>> partitionedParams = parameterList.stream()
                .collect(Collectors.partitioningBy(p -> !ChangeType.DELETE.equals(p.getChangeType())));

        List<OParameterVersionDO> onlineParameters = partitionedParams.get(true);
        List<String> offlineParameterKeys = partitionedParams.get(false).stream()
                .map(OParameterVersionDO::getParameterKey)
                .collect(Collectors.toList());

        return new ParameterPartition(onlineParameters, offlineParameterKeys);
    }

            // 批量获取参数条件
            final Map<String, List<OParameterConditionVersionDO>> paramConditionsMap;
            if (!online.isEmpty()) {
                List<String> parameterIds = online.stream()
                        .map(OParameterVersionDO::getParameterId)
                        .collect(Collectors.toList());

                List<OParameterConditionVersionDO> allConditions = parameterConditionVersionDAO.lambdaQuery()
                        .eq(OParameterConditionVersionDO::getNamespaceId, namespaceId)
                        .in(OParameterConditionVersionDO::getParameterId, parameterIds)
                        .in(OParameterConditionVersionDO::getStatus, Arrays.asList(VersionStatus.RELEASED, VersionStatus.INIT))
                        .ne(OParameterConditionVersionDO::getChangeType, ChangeType.DELETE)
                        .list();

                // 按参数ID分组
                paramConditionsMap = allConditions.stream()
                        .collect(Collectors.groupingBy(OParameterConditionVersionDO::getParameterId));
            } else {
                paramConditionsMap = Collections.emptyMap();
            }

            List<Parameter> params = online.stream()
                    .map(p -> {
                        List<OParameterConditionVersionDO> conditions = paramConditionsMap
                                .getOrDefault(p.getParameterId(), Collections.emptyList());

                        Map<String, OParameterConditionVersionDO> condMap = conditions.stream()
                                .collect(Collectors.toMap(
                                        OParameterConditionVersionDO::getConditionId,
                                        Function.identity(),
                                        (v1, v2) -> VersionStatus.INIT.equals(v1.getStatus()) ? v1 : v2));

                        return buildParameter(p, new ArrayList<>(condMap.values()));
                    })
                    .filter(Objects::nonNull)
                    // 端上依赖排序，加速检索
                    .sorted(Comparator.comparing(Parameter::getKey))
                    .collect(Collectors.toList());

            return GrayConfig.ReleaseOrder.builder()
                    .version(Long.parseLong(version))
                    .parameters(params)
                    .offlineParameters(offline)
                    .build();
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    protected List<VersionStatus> getVersionStatuses() {
        return Arrays.asList(VersionStatus.RELEASED, VersionStatus.INIT);
    }
}
