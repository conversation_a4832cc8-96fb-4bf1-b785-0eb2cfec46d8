package com.taobao.wireless.orange.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.constant.enums.NamespaceStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.ONamespaceDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceDO;
import com.taobao.wireless.orange.manager.model.ONamespaceBO;
import com.taobao.wireless.orange.manager.util.PageUtil;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Optional;

@Service
@Validated
public class NamespaceManager {

    @Autowired
    private ONamespaceDAO namespaceDAO;

    /**
     * 查询命名空间列表，支持分页和条件查询
     *
     * @param query      查询条件
     * @param pagination 分页信息
     * @return 命名空间分页列表结果
     */
    public Page<ONamespaceDO> query(ONamespaceBO query, Pagination pagination) {
        String workerId = ThreadContextUtil.getWorkerId();

        return namespaceDAO.lambdaQuery()
                .ne(ONamespaceDO::getStatus, NamespaceStatus.DELETE)
                .eq(StringUtils.isNotBlank(query.getAppKey()), ONamespaceDO::getAppKey, query.getAppKey())
                .eq(query.getStatus() != null, ONamespaceDO::getStatus, query.getStatus())
                .eq(StringUtils.isNotBlank(query.getName()), ONamespaceDO::getName, query.getName())
                .and(BooleanUtils.isTrue(query.getHasPermission()), queryWrapper -> queryWrapper.like(ONamespaceDO::getOwners, workerId).or().like(ONamespaceDO::getTesters, workerId))
                .and(StringUtils.isNotBlank(query.getKeyword()), wrapper -> wrapper
                        .like(ONamespaceDO::getName, query.getKeyword())
                        .or()
                        .like(ONamespaceDO::getDescription, query.getKeyword())
                )
                .page(PageUtil.build(pagination));
    }

    /**
     * 根据 Namespace ID 获取单个命名空间详情
     *
     * @param namespaceId 命名空间ID
     * @return 命名空间对象
     * @throws CommonException 如果命名空间不存在
     */
    @AttributeValidate
    public ONamespaceDO getByNamespaceId(@NotBlank(message = "命名空间ID不能为空") String namespaceId) {
        return Optional.ofNullable(namespaceDAO.getByNamespaceId(namespaceId))
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST));
    }

    /**
     * 创建新的命名空间
     *
     * @param namespace 命名空间对象
     * @return 创建成功后的命名空间ID
     * @throws CommonException 如果存在同名命名空间或创建失败
     */
    @AttributeValidate
    public String create(@NotNull(message = "命名空间对象不能为空") ONamespaceBO namespace) {
        // 检查是否存在同名命名空间
        checkNamespaceDuplicate(namespace);

        // 设置初始状态
        namespace.setStatus(NamespaceStatus.INIT);

        // 生成 namespaceId
        String namespaceId = SerializeUtil.UUID();
        namespace.setNamespaceId(namespaceId);

        if (!namespaceDAO.save(namespace)) {
            throw new CommonException(ExceptionEnum.CREATE_NAMESPACE_FAIL);
        }

        return namespaceId;
    }

    /**
     * 更新命名空间信息
     *
     * @param namespace 要更新的命名空间对象
     * @return 更新是否成功
     * @throws CommonException 如果命名空间不存在
     */
    @AttributeValidate
    public boolean update(@NotNull(message = "命名空间对象不能为空") ONamespaceBO namespace) {
        // 检查是否存在该命名空间
        Optional.ofNullable(namespaceDAO.getByNamespaceId(namespace.getNamespaceId()))
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST));

        return namespaceDAO.updateByNamespaceId(namespace);
    }

    /**
     * 构建查询条件包装器
     *
     * @param condition 条件对象
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<ONamespaceDO> buildQueryWrapper(ONamespaceBO condition) {
        LambdaQueryWrapper<ONamespaceDO> queryWrapper = new LambdaQueryWrapper<>();
        // 默认过滤已删除的命名空间
        queryWrapper.ne(ONamespaceDO::getStatus, NamespaceStatus.DELETE);

        if (condition != null) {
            // 应用KEY精确匹配
            if (StringUtils.isNotBlank(condition.getAppKey())) {
                queryWrapper.eq(ONamespaceDO::getAppKey, condition.getAppKey());
            }

            // 状态精确匹配
            if (condition.getStatus() != null) {
                queryWrapper.eq(ONamespaceDO::getStatus, condition.getStatus());
            }

            // 名称模糊匹配
            if (StringUtils.isNotBlank(condition.getName())) {
                queryWrapper.like(ONamespaceDO::getName, condition.getName());
            }

            String workerId = ThreadContextUtil.getWorkerId();
            if (BooleanUtils.isTrue(condition.getHasPermission()) && StringUtils.isNotBlank(workerId)) {
                queryWrapper.and(wrapper -> wrapper
                        .like(ONamespaceDO::getOwners, workerId)
                        .or()
                        .like(ONamespaceDO::getTesters, workerId));
            }
        }

        return queryWrapper;
    }

    /**
     * 检查是否存在同名命名空间
     *
     * @param namespace 待检查的命名空间对象
     * @throws CommonException 如果存在同名命名空间
     */
    private void checkNamespaceDuplicate(ONamespaceBO namespace) {
        long count = namespaceDAO.lambdaQuery()
                .eq(ONamespaceDO::getAppKey, namespace.getAppKey())
                .eq(ONamespaceDO::getName, namespace.getName())
                .ne(ONamespaceDO::getStatus, NamespaceStatus.DELETE)
                .count();

        if (count > 0) {
            throw new CommonException(ExceptionEnum.NAMESPACE_NAME_DUPLICATE);
        }
    }
}
