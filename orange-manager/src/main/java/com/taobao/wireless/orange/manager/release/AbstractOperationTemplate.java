package com.taobao.wireless.orange.manager.release;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.OperationStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.dal.enhanced.dao.ONamespaceDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderOperationDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderOperationDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 发布操作模板
 */
public abstract class AbstractReleaseOperationTemplate implements ReleaseOperationStrategy {

    @Autowired
    protected OReleaseOrderDAO releaseOrderDAO;

    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    protected OReleaseOrderOperationDAO releaseOrderOperationDAO;

    /**
     * 定义发布操作的标准流程
     */
    @Transactional(rollbackFor = Exception.class)
    public void execute(ReleaseOperationContext context) {
        Long operationId = null;
        try {
            // 锁管理
            acquireNamespaceLock(context);

            // 数据加载
            loadContextDetail(context);

            // 前置校验
            validation(context);

            // 创建操作记录 (INIT 状态)
            operationId = createOperationRecord(context);

            // 业务逻辑执行
            executeOperation(context);

            // 更新发布单状态
            updateReleaseOrderStatus(context);

            // 更新操作记录 (SUCCESS 状态)
            updateOperationStatus(operationId, OperationStatus.SUCCESS, null);
        } catch (Throwable e) {
            // 异常处理和失败状态记录
            if (operationId != null) {
                updateOperationStatus(operationId, OperationStatus.FAILED, e.getMessage());
            }
            throw e;
        } finally {
            // 释放锁
            releaseNamespaceLock(context);
        }
    }

    /**
     * 前置校验，包含状态校验、权限校验、入参校验
     */
    private void validation(ReleaseOperationContext context) {
        // 状态校验
        validateStatus(context);

        // 权限校验
        validatePermission(context);

        // 入参校验
        validateParameters(context);
    }

    /**
     * 状态校验 - 由子类实现具体的状态校验逻辑
     */
    public abstract void validateStatus(ReleaseOperationContext context);

    /**
     * 权限校验 - 由子类实现具体的权限校验逻辑
     */
    public abstract void validatePermission(ReleaseOperationContext context);

    /**
     * 入参校验 - 由子类实现具体的入参校验逻辑
     */
    public abstract void validateParameters(ReleaseOperationContext context);

    /**
     * 创建操作记录
     */
    protected Long createOperationRecord(ReleaseOperationContext context) {
        OReleaseOrderDO releaseOrder = context.getReleaseOrder();

        OReleaseOrderOperationDO operation = new OReleaseOrderOperationDO();
        operation.setReleaseVersion(context.getReleaseVersion());
        operation.setType(getOperationType());
        operation.setAppKey(releaseOrder.getAppKey());
        operation.setNamespaceId(releaseOrder.getNamespaceId());
        operation.setParams(context.getAdditionalData() != null ? JSON.toJSONString(context.getAdditionalData()) : null);
        operation.setStatus(OperationStatus.INIT);
        releaseOrderOperationDAO.save(operation);
        return operation.getId();
    }

    /**
     * 更新操作记录状态
     */
    protected void updateOperationStatus(Long id, OperationStatus status, String errorMessage) {
        var operation = new OReleaseOrderOperationDO();
        operation.setId(id);
        operation.setStatus(status);
        if (StringUtils.isNotBlank(errorMessage)) {
            operation.setResult(JSON.toJSONString(Map.entry("errorMessage", errorMessage)));
        }
        releaseOrderOperationDAO.updateById(operation);
    }

    /**
     * 获取上下文详情
     */
    protected void loadContextDetail(ReleaseOperationContext context) {
        OReleaseOrderDO releaseOrder = releaseOrderDAO.lambdaQuery()
                .eq(OReleaseOrderDO::getReleaseVersion, context.getReleaseVersion())
                .oneOpt()
                .orElseThrow(() -> new CommonException(ExceptionEnum.RELEASE_ORDER_NOT_EXIST));
        context.setReleaseOrder(releaseOrder);

        ONamespaceDO namespace = namespaceDAO.lambdaQuery()
                .eq(ONamespaceDO::getNamespaceId, releaseOrder.getNamespaceId())
                .oneOpt()
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST));
        context.setNamespace(namespace);
    }

    /**
     * 更新发布单状态的默认实现
     */
    @Override
    public void updateReleaseOrderStatus(ReleaseOperationContext context) {
        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, context.getReleaseVersion())
                .set(OReleaseOrderDO::getStatus, getTargetStatus())
                .update();
    }

    /**
     * 获取 namespace 锁 - 子类可以重写具体实现
     */
    protected void acquireNamespaceLock(ReleaseOperationContext context) {
        // TODO: 实现具体的锁获取逻辑
    }

    /**
     * 释放 namespace 锁 - 子类可以重写具体实现
     */
    protected void releaseNamespaceLock(ReleaseOperationContext context) {
        // TODO: 实现具体的锁释放逻辑
    }
}