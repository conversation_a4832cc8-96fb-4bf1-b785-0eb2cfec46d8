package com.taobao.wireless.orange.manager.release.impl;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.manager.release.AbstractOperationTemplate;
import com.taobao.wireless.orange.manager.release.OperationContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 验证操作策略实现
 */
@Component
public class VerifyStrategy extends AbstractOperationTemplate {

    @Override
    public OperationType getOperationType() {
        return OperationType.VERIFY;
    }

    @Override
    public void validateStatus(OperationContext context) {
        var releaseOrder = context.getReleaseOrder();

        if (!ReleaseOrderStatus.WAIT_VERIFY.equals(releaseOrder.getStatus())) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_INVALID);
        }
    }

    @Override
    public void validatePermission(OperationContext context) {
        var namespace = context.getNamespace();
        String workerId = ThreadContextUtil.getWorkerId();

        if (StringUtils.isBlank(workerId) || !namespace.getTesters().contains(workerId)) {
            throw new CommonException(ExceptionEnum.NO_PERMISSION);
        }
    }

    @Override
    public void validateParameters(OperationContext context) {
    }

    @Override
    public void executeOperation(OperationContext context) {
    }

    @Override
    public ReleaseOrderStatus getTargetStatus() {
        return ReleaseOrderStatus.VERIFY_PASS;
    }
}